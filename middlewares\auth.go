package middlewares

import (
	"net/http"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

func Auth() gin.HandlerFunc {
	return func(c *gin.Context) {
		session := sessions.Default(c)
		loginId := session.Get("loginId")
		c.Set("loginId", loginId)
		provider := session.Get("p")
		c.Set("p", provider)
		c.Set("uuid", provider.(string)+":"+loginId.(string))
		if provider == nil || loginId == nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		}

		c.Next()
	}
}
